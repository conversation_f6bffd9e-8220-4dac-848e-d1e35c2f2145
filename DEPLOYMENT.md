# 🚀 Deployment Guide

Panduan untuk deploy personal website ke berbagai platform hosting.

## 📋 Prerequisites

- Node.js 18+ 
- npm atau yarn
- Git

## 🏗️ Build untuk Production

```bash
# Install dependencies
npm install

# Build aplikasi
npm run build

# Preview build (opsional)
npm run preview
```

## 🌐 Platform Deployment

### 1. Vercel (Recommended)

Vercel adalah platform yang sangat cocok untuk React apps dengan setup yang mudah.

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Atau deploy langsung dari GitHub
# 1. Push code ke GitHub
# 2. Connect repository di vercel.com
# 3. Auto-deploy setiap push
```

**Konfigurasi Vercel** (`vercel.json`):
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite"
}
```

### 2. Netlify

```bash
# Build command: npm run build
# Publish directory: dist

# Atau menggunakan Netlify CLI
npm install -g netlify-cli
netlify deploy --prod --dir=dist
```

### 3. GitHub Pages

```bash
# Install gh-pages
npm install --save-dev gh-pages

# Tambahkan script di package.json
"homepage": "https://username.github.io/repository-name",
"scripts": {
  "predeploy": "npm run build",
  "deploy": "gh-pages -d dist"
}

# Deploy
npm run deploy
```

### 4. Firebase Hosting

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login dan init
firebase login
firebase init hosting

# Deploy
firebase deploy
```

## ⚙️ Environment Variables

Untuk production, set environment variables berikut:

```bash
# .env.production
VITE_APP_TITLE="Rofikul Azmi - Portfolio"
VITE_APP_DESCRIPTION="Full Stack Developer & UI/UX Designer"
VITE_APP_URL="https://yourdomain.com"
```

## 🔧 Optimasi Performance

### 1. Image Optimization
- Gunakan format WebP untuk gambar
- Compress gambar sebelum upload
- Implementasi lazy loading

### 2. Code Splitting
- Sudah dikonfigurasi di `vite.config.js`
- Vendor chunks terpisah untuk caching yang lebih baik

### 3. CDN Setup
- Upload assets ke CDN (Cloudinary, AWS S3, dll)
- Update URL di konfigurasi

## 📊 Analytics & Monitoring

### Google Analytics
Tambahkan di `index.html`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### Performance Monitoring
- Lighthouse CI untuk monitoring performance
- Web Vitals tracking
- Error tracking dengan Sentry

## 🔒 Security Headers

Tambahkan security headers di hosting platform:

```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: default-src 'self'
```

## 🌍 Custom Domain

### Setup DNS
1. Beli domain dari registrar (Namecheap, GoDaddy, dll)
2. Setup DNS records:
   - A record: @ → IP hosting
   - CNAME: www → domain.com
3. Configure SSL certificate

### Domain Configuration
- Vercel: Add domain di dashboard
- Netlify: Domain settings → Add custom domain
- GitHub Pages: Settings → Pages → Custom domain

## 📱 PWA Setup (Optional)

Untuk membuat Progressive Web App:

```bash
# Install Vite PWA plugin
npm install -D vite-plugin-pwa

# Update vite.config.js
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      }
    })
  ]
})
```

## 🚨 Troubleshooting

### Build Errors
- Check Node.js version compatibility
- Clear npm cache: `npm cache clean --force`
- Delete node_modules dan reinstall

### Deployment Issues
- Verify build output di folder `dist`
- Check environment variables
- Review hosting platform logs

### Performance Issues
- Analyze bundle size: `npm run build -- --analyze`
- Optimize images dan assets
- Enable gzip compression

## 📞 Support

Jika mengalami masalah deployment:
1. Check dokumentasi platform hosting
2. Review error logs
3. Contact support platform hosting
4. Community forums dan Stack Overflow

---

**Happy Deploying! 🎉**
