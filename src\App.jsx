import { useEffect, useRef, useState } from 'react';
// import LocomotiveScroll from 'locomotive-scroll';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Components
import ErrorBoundary from './components/ErrorBoundary';
import LoadingScreen from './components/LoadingScreen';
import Navigation from './components/Navigation';
import CustomCursor from './components/CustomCursor';
import Hero from './components/Hero';
import About from './components/About';
import Portfolio from './components/Portfolio';
import Contact from './components/Contact';
import Footer from './components/Footer';

// Development utilities
if (import.meta.env.DEV) {
  import('./utils/testHelpers.js');
}

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

function App() {
  const scrollRef = useRef(null);
  const locomotiveScrollRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isLoading) return;

    // Simple smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Initialize ScrollTrigger
    ScrollTrigger.refresh();

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [isLoading]);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <LoadingScreen onComplete={handleLoadingComplete} />;
  }

  return (
    <ErrorBoundary>
      <div className="App">
        <CustomCursor />
        <Navigation />
        <div ref={scrollRef}>
          <Hero />
          <About />
          <Portfolio />
          <Contact />
          <Footer />
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default App;
