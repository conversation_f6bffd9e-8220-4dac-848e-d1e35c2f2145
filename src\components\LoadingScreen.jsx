import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

const LoadingScreen = ({ onComplete }) => {
  const loadingRef = useRef(null);
  const logoRef = useRef(null);
  const progressRef = useRef(null);
  const textRef = useRef(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const loading = loadingRef.current;
    const logo = logoRef.current;
    const progressBar = progressRef.current;
    const text = textRef.current;

    // Initial setup
    gsap.set([logo, text], { opacity: 0, y: 50 });
    gsap.set(progressBar, { scaleX: 0 });

    // Entrance animation
    const tl = gsap.timeline();
    
    tl.to(logo, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power3.out"
    })
    .to(text, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.5");

    // Progress simulation
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15;
        if (newProgress >= 100) {
          clearInterval(progressInterval);
          
          // Complete animation
          gsap.to(progressBar, {
            scaleX: 1,
            duration: 0.5,
            ease: "power2.out",
            onComplete: () => {
              setTimeout(() => {
                // Exit animation
                gsap.to(loading, {
                  opacity: 0,
                  duration: 0.8,
                  ease: "power2.inOut",
                  onComplete: () => {
                    onComplete();
                  }
                });
              }, 500);
            }
          });
          
          return 100;
        }
        return newProgress;
      });
    }, 100);

    // Update progress bar
    gsap.to(progressBar, {
      scaleX: progress / 100,
      duration: 0.3,
      ease: "power2.out"
    });

    return () => {
      clearInterval(progressInterval);
    };
  }, [progress, onComplete]);

  return (
    <div 
      ref={loadingRef}
      className="fixed inset-0 z-[9999] bg-dark-900 flex items-center justify-center"
    >
      {/* Background Animation */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-primary-900"></div>
        
        {/* Animated Particles */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary-400 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 6}s`,
                animationDuration: `${4 + Math.random() * 4}s`
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Loading Content */}
      <div className="relative z-10 text-center">
        {/* Logo */}
        <div ref={logoRef} className="mb-8">
          <h1 className="text-6xl sm:text-7xl font-display font-bold text-white">
            R<span className="text-gradient">F</span>
          </h1>
          <div className="w-20 h-1 bg-gradient-to-r from-primary-400 to-purple-500 mx-auto mt-4 rounded-full"></div>
        </div>

        {/* Loading Text */}
        <div ref={textRef} className="mb-8">
          <p className="text-xl text-gray-300 mb-2">Loading Portfolio</p>
          <p className="text-sm text-gray-500">Preparing amazing experience...</p>
        </div>

        {/* Progress Bar */}
        <div className="w-64 mx-auto">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Loading</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full h-1 bg-gray-700 rounded-full overflow-hidden">
            <div 
              ref={progressRef}
              className="h-full bg-gradient-to-r from-primary-400 to-purple-500 rounded-full origin-left"
              style={{ transform: `scaleX(${progress / 100})` }}
            ></div>
          </div>
        </div>

        {/* Loading Dots */}
        <div className="flex justify-center space-x-2 mt-8">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="w-2 h-2 bg-primary-400 rounded-full animate-pulse"
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1.5s'
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Corner Decorations */}
      <div className="absolute top-8 left-8">
        <div className="w-16 h-16 border-l-2 border-t-2 border-primary-400 opacity-30"></div>
      </div>
      <div className="absolute top-8 right-8">
        <div className="w-16 h-16 border-r-2 border-t-2 border-primary-400 opacity-30"></div>
      </div>
      <div className="absolute bottom-8 left-8">
        <div className="w-16 h-16 border-l-2 border-b-2 border-primary-400 opacity-30"></div>
      </div>
      <div className="absolute bottom-8 right-8">
        <div className="w-16 h-16 border-r-2 border-b-2 border-primary-400 opacity-30"></div>
      </div>
    </div>
  );
};

export default LoadingScreen;
