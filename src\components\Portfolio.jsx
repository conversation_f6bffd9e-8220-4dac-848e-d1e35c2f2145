import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const Portfolio = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const projectsRef = useRef(null);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const projects = projectsRef.current;

    // Title animation
    gsap.fromTo(title, 
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Projects animation
    gsap.fromTo(projects.children, 
      { y: 80, opacity: 0, scale: 0.9 },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: projects,
          start: "top 90%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "Platform e-commerce modern dengan fitur lengkap termasuk payment gateway, inventory management, dan analytics dashboard.",
      technologies: ["React.js", "Node.js", "MongoDB", "Stripe"],
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "Aplikasi manajemen tugas dengan fitur real-time collaboration, drag & drop interface, dan notifikasi push.",
      technologies: ["Vue.js", "Express.js", "Socket.io", "PostgreSQL"],
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    },
    {
      id: 3,
      title: "Learning Management System",
      description: "Platform pembelajaran online dengan fitur video streaming, quiz interaktif, dan progress tracking.",
      technologies: ["React.js", "Python", "Django", "AWS"],
      image: "https://images.unsplash.com/photo-*************-473c47e087f8?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    },
    {
      id: 4,
      title: "Real Estate Website",
      description: "Website real estate dengan fitur pencarian properti, virtual tour, dan sistem booking appointment.",
      technologies: ["Next.js", "TypeScript", "Prisma", "Vercel"],
      image: "https://images.unsplash.com/photo-**********-ce09059eeffa?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    },
    {
      id: 5,
      title: "Mobile Banking App",
      description: "Aplikasi mobile banking dengan fitur transfer, pembayaran, dan investment tracking yang aman.",
      technologies: ["React Native", "Node.js", "Redis", "Docker"],
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    },
    {
      id: 6,
      title: "Analytics Dashboard",
      description: "Dashboard analytics dengan visualisasi data real-time, reporting, dan machine learning insights.",
      technologies: ["React.js", "D3.js", "Python", "TensorFlow"],
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop",
      link: "#",
      github: "#"
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="section-padding bg-dark-900 text-white"

      data-section="portfolio"
    >
      <div className="max-w-7xl mx-auto">
        <h2 
          ref={titleRef}
          className="text-4xl sm:text-5xl font-display font-bold text-center mb-16"
        >
          Portfolio <span className="text-gradient">Saya</span>
        </h2>

        <div 
          ref={projectsRef}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project) => (
            <div 
              key={project.id}
              className="group relative bg-dark-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-500 cursor-pointer"
            >
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <img 
                  src={project.image} 
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-900 via-transparent to-transparent opacity-60"></div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3 group-hover:text-primary-400 transition-colors">
                  {project.title}
                </h3>
                <p className="text-gray-400 text-sm mb-4 leading-relaxed">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 bg-primary-600 bg-opacity-20 text-primary-400 text-xs rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Project Links */}
                <div className="flex gap-4">
                  <a 
                    href={project.link}
                    className="flex-1 text-center py-2 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    Live Demo
                  </a>
                  <a 
                    href={project.github}
                    className="flex-1 text-center py-2 glass-effect text-white text-sm font-medium rounded-lg transition-all hover:bg-white hover:bg-opacity-10"
                  >
                    GitHub
                  </a>
                </div>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary-600 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
            </div>
          ))}
        </div>

        {/* View More Button */}
        <div className="text-center mt-12">
          <button className="px-8 py-4 glass-effect text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
            Lihat Semua Proyek
          </button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
