import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-dark-900 flex items-center justify-center px-4">
          <div className="text-center">
            <div className="mb-8">
              <h1 className="text-6xl font-display font-bold text-white mb-4">
                Oops!
              </h1>
              <div className="w-20 h-1 bg-gradient-to-r from-red-400 to-red-600 mx-auto rounded-full"></div>
            </div>
            
            <h2 className="text-2xl font-semibold text-white mb-4">
              Something went wrong
            </h2>
            
            <p className="text-gray-400 mb-8 max-w-md mx-auto">
              We're sorry for the inconvenience. Please try refreshing the page 
              or contact us if the problem persists.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                Refresh Page
              </button>
              
              <button
                onClick={() => window.history.back()}
                className="px-8 py-4 glass-effect text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                Go Back
              </button>
            </div>
            
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-8 text-left max-w-2xl mx-auto">
                <summary className="text-gray-400 cursor-pointer hover:text-white transition-colors">
                  Error Details (Development Only)
                </summary>
                <div className="mt-4 p-4 bg-dark-800 rounded-lg text-sm text-red-400 overflow-auto">
                  <pre>{this.state.error && this.state.error.toString()}</pre>
                  <pre>{this.state.errorInfo.componentStack}</pre>
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
