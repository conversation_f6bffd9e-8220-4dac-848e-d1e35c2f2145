<!doctype html>
<html lang="id">
  <head>
    <script type="module">import { injectIntoGlobalHook } from "/@react-refresh";
injectIntoGlobalHook(window);
window.$RefreshReg$ = () => {};
window.$RefreshSig$ = () => (type) => type;</script>

    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Rofikul Azmi - Full Stack Developer & UI/UX Designer. Portfolio website dengan teknologi modern dan animasi sinematik." />
    <meta name="keywords" content="Full Stack Developer, UI/UX Designer, React, Node.js, Portfolio, Web Development" />
    <meta name="author" content="Rofikul Azmi" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Rofikul Azmi - Full Stack Developer" />
    <meta property="og:description" content="Portfolio website dengan teknologi modern dan animasi sinematik" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://rfiklz.my.id" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Rofikul Azmi - Full Stack Developer" />
    <meta name="twitter:description" content="Portfolio website dengan teknologi modern dan animasi sinematik" />

    <title>Rofikul Azmi - Full Stack Developer & UI/UX Designer</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
