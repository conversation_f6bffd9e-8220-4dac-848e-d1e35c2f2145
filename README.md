# 🚀 Personal Website - R<PERSON>ikul Azmi

Personal website dengan nuansa visual yang sinematik dan interaktif menggunakan React, Tailwind CSS, GSAP, dan Locomotive Scroll.

## ✨ Fitur Utama

- **🎬 Animasi Sinematik**: Menggunakan GSAP + ScrollTrigger untuk animasi yang smooth dan dramatis
- **🌊 Smooth Scrolling**: Implementasi Locomotive Scroll untuk pengalaman scrolling yang halus
- **💎 Glass Effect**: Efek kaca dinamis menggunakan CSS backdrop-filter
- **📱 Responsive Design**: Desain yang optimal di semua perangkat
- **⚡ Performance Optimized**: Built dengan Vite untuk development dan build yang cepat
- **🎨 Modern UI/UX**: Desain yang clean dan profesional dengan Tailwind CSS

## 🛠️ Teknologi yang Digunakan

- **Frontend Framework**: React 19
- **Styling**: Tailwind CSS
- **Animasi**: GSAP + ScrollTrigger
- **Smooth Scroll**: Locomotive Scroll
- **Build Tool**: Vite
- **Package Manager**: npm

## 📦 Instalasi

1. Clone repository ini:
```bash
git clone <repository-url>
cd personal-web
```

2. Install dependencies:
```bash
npm install
```

3. Jalankan development server:
```bash
npm run dev
```

4. Buka browser dan akses `http://localhost:5173`

## 🏗️ Struktur Proyek

```
personal-web/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Hero.jsx          # Hero section dengan animasi
│   │   ├── About.jsx         # Section tentang saya
│   │   ├── Portfolio.jsx     # Portfolio proyek
│   │   ├── Contact.jsx       # Form kontak
│   │   └── Footer.jsx        # Footer website
│   ├── App.jsx               # Main app component
│   ├── main.jsx              # Entry point
│   └── index.css             # Global styles
├── tailwind.config.js        # Konfigurasi Tailwind
├── postcss.config.js         # Konfigurasi PostCSS
└── package.json
```

## 🎨 Komponen Utama

### Hero Section
- Animasi teks yang dramatis dengan GSAP
- Background gradient dengan partikel animasi
- Glass effect overlay
- Scroll indicator

### About Section
- Animasi scroll-triggered untuk konten
- Progress bar untuk skills
- Layout responsive dengan grid

### Portfolio Section
- Grid layout untuk proyek
- Hover effects dengan transformasi
- Staggered animation untuk cards

### Contact Section
- Form kontak dengan validasi
- Informasi kontak dengan ikon
- Glass effect styling

### Footer
- Links navigasi dan sosial media
- Newsletter subscription
- Back to top button

## 🚀 Scripts

- `npm run dev` - Menjalankan development server
- `npm run build` - Build untuk production
- `npm run preview` - Preview build production
- `npm run lint` - Menjalankan ESLint

## 🎯 Fitur Animasi

- **Scroll-triggered animations**: Elemen muncul saat di-scroll
- **Parallax effects**: Background bergerak dengan kecepatan berbeda
- **Staggered animations**: Animasi berurutan untuk multiple elemen
- **Hover interactions**: Efek hover yang smooth dan responsif
- **Loading animations**: Animasi saat halaman pertama kali dimuat

## 📱 Responsive Design

Website ini dioptimalkan untuk berbagai ukuran layar:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🔧 Kustomisasi

### Warna
Edit `tailwind.config.js` untuk mengubah color palette:
```javascript
colors: {
  primary: {
    // Your custom colors
  }
}
```

### Animasi
Modifikasi animasi GSAP di setiap komponen sesuai kebutuhan.

### Konten
Update konten di setiap komponen untuk menyesuaikan dengan informasi pribadi Anda.

## 📄 License

MIT License - feel free to use this project for your own portfolio!

## 🤝 Contributing

Contributions, issues, and feature requests are welcome!

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/rofikul](https://linkedin.com/in/rofikul)
- **Website**: [rfiklz.my.id](https://rfiklz.my.id)
