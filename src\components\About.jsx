import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const skillsRef = useRef(null);
  const imageRef = useRef(null);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const content = contentRef.current;
    const skills = skillsRef.current;
    const image = imageRef.current;

    // Title animation
    gsap.fromTo(title, 
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Content animation
    gsap.fromTo(content, 
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        delay: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Skills animation
    gsap.fromTo(skills.children, 
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: skills,
          start: "top 90%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Image animation
    gsap.fromTo(image, 
      { scale: 0.8, opacity: 0 },
      {
        scale: 1,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const skills = [
    { name: 'React.js', level: 90, color: 'bg-blue-500' },
    { name: 'Node.js', level: 85, color: 'bg-green-500' },
    { name: 'TypeScript', level: 80, color: 'bg-blue-600' },
    { name: 'Python', level: 75, color: 'bg-yellow-500' },
    { name: 'UI/UX Design', level: 85, color: 'bg-purple-500' },
    { name: 'MongoDB', level: 80, color: 'bg-green-600' },
  ];

  return (
    <section
      ref={sectionRef}
      className="section-padding bg-dark-800 text-white"

      data-section="about"
    >
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div>
            <h2 
              ref={titleRef}
              className="text-4xl sm:text-5xl font-display font-bold mb-8"
            >
              Tentang <span className="text-gradient">Saya</span>
            </h2>
            
            <div ref={contentRef} className="space-y-6">
              <p className="text-lg text-gray-300 leading-relaxed">
                Saya adalah seorang Full Stack Developer dengan passion yang mendalam 
                dalam menciptakan solusi digital yang inovatif dan user-friendly. 
                Dengan pengalaman lebih dari 3 tahun di industri teknologi, saya 
                telah mengembangkan berbagai aplikasi web dan mobile yang membantu 
                bisnis mencapai tujuan mereka.
              </p>
              
              <p className="text-lg text-gray-300 leading-relaxed">
                Keahlian saya mencakup pengembangan frontend dengan React.js, Vue.js, 
                dan backend dengan Node.js, Python. Saya juga memiliki pengalaman 
                dalam UI/UX design dan selalu berusaha menciptakan pengalaman pengguna 
                yang optimal.
              </p>
              
              <p className="text-lg text-gray-300 leading-relaxed">
                Saya percaya bahwa teknologi harus dapat memecahkan masalah nyata 
                dan memberikan nilai tambah bagi pengguna. Oleh karena itu, saya 
                selalu fokus pada kualitas, performa, dan user experience dalam 
                setiap proyek yang saya kerjakan.
              </p>
            </div>

            {/* Skills */}
            <div className="mt-12">
              <h3 className="text-2xl font-semibold mb-6">Keahlian</h3>
              <div ref={skillsRef} className="space-y-4">
                {skills.map((skill, index) => (
                  <div key={index} className="skill-item">
                    <div className="flex justify-between mb-2">
                      <span className="text-white font-medium">{skill.name}</span>
                      <span className="text-gray-400">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${skill.color} transition-all duration-1000 ease-out`}
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="flex justify-center lg:justify-end">
            <div 
              ref={imageRef}
              className="relative w-80 h-80 rounded-2xl overflow-hidden glass-effect"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-purple-600 opacity-20"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-64 h-64 bg-gradient-to-br from-primary-400 to-purple-500 rounded-full opacity-30 animate-pulse"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-6xl font-bold text-white opacity-50">RF</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
