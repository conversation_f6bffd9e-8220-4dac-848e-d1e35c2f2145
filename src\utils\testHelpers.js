// Test utilities untuk development dan debugging

export const testAnimations = () => {
  console.log('🎬 Testing GSAP animations...');
  
  // Test GSAP availability
  if (typeof gsap !== 'undefined') {
    console.log('✅ GSAP loaded successfully');
    
    // Test simple animation
    gsap.to('.test-element', {
      duration: 1,
      x: 100,
      onComplete: () => console.log('✅ GSAP animation test completed')
    });
  } else {
    console.error('❌ GSAP not loaded');
  }
};

export const testScrollTrigger = () => {
  console.log('📜 Testing ScrollTrigger...');
  
  if (typeof ScrollTrigger !== 'undefined') {
    console.log('✅ ScrollTrigger loaded successfully');
    console.log('📊 Active ScrollTriggers:', ScrollTrigger.getAll().length);
  } else {
    console.error('❌ ScrollTrigger not loaded');
  }
};

export const testResponsive = () => {
  console.log('📱 Testing responsive design...');
  
  const breakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  };
  
  const width = window.innerWidth;
  
  if (width < breakpoints.mobile) {
    console.log('📱 Mobile view detected');
  } else if (width < breakpoints.tablet) {
    console.log('📱 Tablet view detected');
  } else {
    console.log('🖥️ Desktop view detected');
  }
  
  return {
    width,
    isMobile: width < breakpoints.mobile,
    isTablet: width >= breakpoints.mobile && width < breakpoints.tablet,
    isDesktop: width >= breakpoints.tablet
  };
};

export const testPerformance = () => {
  console.log('⚡ Testing performance...');
  
  // Test loading time
  const loadTime = performance.now();
  console.log(`⏱️ Page load time: ${loadTime.toFixed(2)}ms`);
  
  // Test memory usage (if available)
  if (performance.memory) {
    const memory = performance.memory;
    console.log('🧠 Memory usage:', {
      used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`
    });
  }
  
  // Test FPS (simplified)
  let fps = 0;
  let lastTime = performance.now();
  
  const measureFPS = () => {
    const currentTime = performance.now();
    fps = 1000 / (currentTime - lastTime);
    lastTime = currentTime;
    
    if (fps < 30) {
      console.warn('⚠️ Low FPS detected:', fps.toFixed(2));
    }
    
    requestAnimationFrame(measureFPS);
  };
  
  measureFPS();
};

export const testAccessibility = () => {
  console.log('♿ Testing accessibility...');
  
  const issues = [];
  
  // Check for alt text on images
  const images = document.querySelectorAll('img');
  images.forEach((img, index) => {
    if (!img.alt) {
      issues.push(`Image ${index + 1} missing alt text`);
    }
  });
  
  // Check for proper heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let lastLevel = 0;
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      issues.push(`Heading level skip detected at heading ${index + 1}`);
    }
    lastLevel = level;
  });
  
  // Check for focus indicators
  const focusableElements = document.querySelectorAll(
    'a, button, input, textarea, select, [tabindex]'
  );
  
  if (issues.length === 0) {
    console.log('✅ No accessibility issues found');
  } else {
    console.warn('⚠️ Accessibility issues:', issues);
  }
  
  return {
    totalImages: images.length,
    totalHeadings: headings.length,
    totalFocusableElements: focusableElements.length,
    issues
  };
};

export const runAllTests = () => {
  console.log('🧪 Running all tests...');
  console.log('================================');
  
  testAnimations();
  testScrollTrigger();
  testResponsive();
  testPerformance();
  testAccessibility();
  
  console.log('================================');
  console.log('✅ All tests completed');
};

// Auto-run tests in development
if (import.meta.env.DEV) {
  window.testHelpers = {
    testAnimations,
    testScrollTrigger,
    testResponsive,
    testPerformance,
    testAccessibility,
    runAllTests
  };
  
  console.log('🧪 Test helpers available in window.testHelpers');
  console.log('Run window.testHelpers.runAllTests() to test everything');
}
