import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const Hero = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const backgroundRef = useRef(null);

  useEffect(() => {
    const hero = heroRef.current;
    const title = titleRef.current;
    const subtitle = subtitleRef.current;
    const cta = ctaRef.current;
    const background = backgroundRef.current;

    // Initial animation
    gsap.set([title, subtitle, cta], { y: 100, opacity: 0 });
    gsap.set(background, { scale: 1.2, opacity: 0 });

    const tl = gsap.timeline();
    
    tl.to(background, { 
      scale: 1, 
      opacity: 1, 
      duration: 2, 
      ease: "power2.out" 
    })
    .to(title, { 
      y: 0, 
      opacity: 1, 
      duration: 1.2, 
      ease: "power3.out" 
    }, "-=1.5")
    .to(subtitle, { 
      y: 0, 
      opacity: 1, 
      duration: 1, 
      ease: "power3.out" 
    }, "-=0.8")
    .to(cta, { 
      y: 0, 
      opacity: 1, 
      duration: 0.8, 
      ease: "power3.out" 
    }, "-=0.5");

    // Parallax effect
    gsap.to(background, {
      yPercent: -50,
      ease: "none",
      scrollTrigger: {
        trigger: hero,
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });

    // Text parallax
    gsap.to([title, subtitle], {
      yPercent: -30,
      ease: "none",
      scrollTrigger: {
        trigger: hero,
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"

      data-section="home"
    >
      {/* Animated Background */}
      <div 
        ref={backgroundRef}
        className="absolute inset-0 z-0"
      >
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-primary-900"></div>
        
        {/* Animated Particles */}
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary-400 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 6}s`,
                animationDuration: `${4 + Math.random() * 4}s`
              }}
            ></div>
          ))}
        </div>

        {/* Glass Effect Overlay */}
        <div className="absolute inset-0 glass-effect opacity-20"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        <h1 
          ref={titleRef}
          className="text-5xl sm:text-6xl lg:text-7xl font-display font-bold text-white mb-6"
        >
          <span className="block">Rofikul</span>
          <span className="block text-gradient">Azmi</span>
        </h1>
        
        <p 
          ref={subtitleRef}
          className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed"
        >
          Full Stack Developer & UI/UX Designer yang passionate dalam menciptakan 
          pengalaman digital yang memukau dan fungsional.
        </p>
        
        <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            className="px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            data-cursor="pointer"
            data-cursor-text="View"
          >
            Lihat Portfolio
          </button>
          <button
            className="px-8 py-4 glass-effect text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            data-cursor="pointer"
            data-cursor-text="Contact"
          >
            Hubungi Saya
          </button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
