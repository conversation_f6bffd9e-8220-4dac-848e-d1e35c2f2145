import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

const CustomCursor = () => {
  const cursorRef = useRef(null);
  const followerRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);
  const [cursorText, setCursorText] = useState('');

  useEffect(() => {
    const cursor = cursorRef.current;
    const follower = followerRef.current;

    if (!cursor || !follower) return;

    // Mouse move handler
    const handleMouseMove = (e) => {
      gsap.to(cursor, {
        x: e.clientX,
        y: e.clientY,
        duration: 0.1,
        ease: "power2.out"
      });

      gsap.to(follower, {
        x: e.clientX,
        y: e.clientY,
        duration: 0.3,
        ease: "power2.out"
      });
    };

    // Mouse enter handler for interactive elements
    const handleMouseEnter = (e) => {
      const target = e.target;
      
      if (target.matches('a, button, [data-cursor="pointer"]')) {
        setIsHovering(true);
        setCursorText(target.dataset.cursorText || '');
        
        gsap.to(cursor, {
          scale: 0.5,
          duration: 0.3,
          ease: "power2.out"
        });
        
        gsap.to(follower, {
          scale: 2,
          duration: 0.3,
          ease: "power2.out"
        });
      }
      
      if (target.matches('[data-cursor="view"]')) {
        setCursorText('View');
        setIsHovering(true);
      }
      
      if (target.matches('[data-cursor="drag"]')) {
        setCursorText('Drag');
        setIsHovering(true);
      }
    };

    // Mouse leave handler
    const handleMouseLeave = (e) => {
      const target = e.target;
      
      if (target.matches('a, button, [data-cursor]')) {
        setIsHovering(false);
        setCursorText('');
        
        gsap.to([cursor, follower], {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    };

    // Mouse down/up handlers
    const handleMouseDown = () => {
      gsap.to([cursor, follower], {
        scale: 0.8,
        duration: 0.1,
        ease: "power2.out"
      });
    };

    const handleMouseUp = () => {
      gsap.to([cursor, follower], {
        scale: isHovering ? [0.5, 2] : [1, 1],
        duration: 0.1,
        ease: "power2.out"
      });
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    // Hide default cursor
    document.body.style.cursor = 'none';

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseenter', handleMouseEnter, true);
      document.removeEventListener('mouseleave', handleMouseLeave, true);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'auto';
    };
  }, [isHovering]);

  return (
    <>
      {/* Main Cursor */}
      <div
        ref={cursorRef}
        className="fixed top-0 left-0 w-4 h-4 bg-primary-400 rounded-full pointer-events-none z-[9999] mix-blend-difference"
        style={{ transform: 'translate(-50%, -50%)' }}
      />

      {/* Cursor Follower */}
      <div
        ref={followerRef}
        className={`fixed top-0 left-0 w-8 h-8 border-2 border-primary-400 rounded-full pointer-events-none z-[9998] transition-colors duration-300 ${
          isHovering ? 'border-white bg-primary-400 bg-opacity-20' : ''
        }`}
        style={{ transform: 'translate(-50%, -50%)' }}
      >
        {/* Cursor Text */}
        {cursorText && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs font-medium text-white whitespace-nowrap">
            {cursorText}
          </div>
        )}
      </div>
    </>
  );
};

export default CustomCursor;
